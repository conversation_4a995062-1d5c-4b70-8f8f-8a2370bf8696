# ==========================================
# 完整的伪静态规则 (Nginx)
# ==========================================

# 首页、分类浏览、数据归档、最近更新、排行榜、意见反馈
rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)(/?)$ /index.php?mod=$1;

# 最近更新
rewrite ^/update/(\d+)\.html$ /index.php?mod=update&days=$1;
rewrite ^/update/(\d+)-(\d+)\.html$ /index.php?mod=update&days=$1&page=$2;

# 数据归档
rewrite ^/archives/(\d+)\.html$ /index.php?mod=archives&date=$1;
rewrite ^/archives/(\d+)-(\d+)\.html$ /index.php?mod=archives&date=$1&page=$2;

# 站内搜索
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/search/(name|url|tags|intro|br|pr|art)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;
rewrite ^/(br|pr)/(.+)-(\d+)\.html$ /index.php?mod=search&type=$1&query=$2&page=$3;
rewrite ^/(br|pr)/(.+)\.html$ /index.php?mod=search&type=$1&query=$2;

# 站点详细页面（多种格式支持）
rewrite ^/view/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo/(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/siteinfo-(\d+)\.html$ /index.php?mod=siteinfo&wid=$1;
rewrite ^/site/(\d+)-(.+)(/?)\.html$ /index.php?mod=siteinfo&wid=$1;

# 文章详细页面
rewrite ^/artinfo/(\d+)\.html$ /index.php?mod=artinfo&aid=$1;

# 链接详细页面
rewrite ^/linkinfo/(\d+)\.html$ /index.php?mod=linkinfo&lid=$1;

# 自定义页面
rewrite ^/diypage/(\d+)\.html$ /index.php?mod=diypage&pid=$1;

# RSS订阅
rewrite ^/rssfeed/(\d+)\.html$ /index.php?mod=rssfeed&cid=$1;

# 网站地图
rewrite ^/sitemap/(\d+)\.html$ /index.php?mod=sitemap&cid=$1;

# 网站目录分类页面
rewrite ^/webdir/(.+)/(\d+)\.html$ /index.php?mod=webdir&cid=$2;
rewrite ^/webdir/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&page=$3;
rewrite ^/webdir/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=webdir&cid=$2&sort=$3&page=$4;

# 友情链接分类页面
rewrite ^/weblink/(.+)/(\d+)\.html$ /index.php?mod=weblink&cid=$2;
rewrite ^/weblink/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&page=$3;
rewrite ^/weblink/(.+)/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=weblink&cid=$2&sort=$3&page=$4;

# 文章分类页面
rewrite ^/article/(.+)/(\d+)\.html$ /index.php?mod=article&cid=$2;
rewrite ^/article/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=article&cid=$2&page=$3;

# RSS分类订阅
rewrite ^/rssfeed/(.+)/(\d+)\.html$ /index.php?mod=rssfeed&cid=$2;
rewrite ^/rssfeed/(.+)/(\d+)-(\d+)\.html$ /index.php?mod=rssfeed&cid=$2&page=$3;

# VIP相关页面
rewrite ^/vip/?$ /index.php?mod=vip_list;
rewrite ^/vip/list/?$ /index.php?mod=vip_list;
rewrite ^/vip/list/(\d+)/?$ /index.php?mod=vip_list&page=$1;
rewrite ^/vip/category/(\d+)/?$ /index.php?mod=vip_list&cid=$1;
rewrite ^/vip/category/(\d+)/(\d+)/?$ /index.php?mod=vip_list&cid=$1&page=$2;
rewrite ^/vip/detail/(\d+)/?$ /index.php?mod=vip_detail&id=$1;

# 待审核页面
rewrite ^/pending/?$ /index.php?mod=pending;
rewrite ^/pending/(\d+)/?$ /index.php?mod=pending&page=$1;
rewrite ^/pending/detail/(\d+)/?$ /index.php?mod=pending_detail&id=$1;

# 黑名单页面
rewrite ^/blacklist/?$ /index.php?mod=blacklist;
rewrite ^/blacklist/(\d+)/?$ /index.php?mod=blacklist&page=$1;
rewrite ^/blacklist/detail/(\d+)/?$ /index.php?mod=blacklist_detail&id=$1;

# 被拒绝页面
rewrite ^/rejected/?$ /index.php?mod=rejected;
rewrite ^/rejected/(\d+)/?$ /index.php?mod=rejected&page=$1;
rewrite ^/rejected/detail/(\d+)/?$ /index.php?mod=rejected_detail&id=$1;

# 网站提交页面
rewrite ^/addurl/?$ /index.php?mod=addurl;
rewrite ^/quicksubmit/?$ /index.php?mod=quicksubmit;

# 数据统计页面
rewrite ^/datastats/?$ /index.php?mod=datastats;

# AJAX和API接口
rewrite ^/ajaxget/?$ /index.php?mod=ajaxget;
rewrite ^/getdata/?$ /index.php?mod=getdata;
rewrite ^/api/?$ /index.php?mod=api;

# 分类页面（带排序）
rewrite ^/category/(\d+)\.html$ /index.php?mod=category&cid=$1;
rewrite ^/category/(\d+)-(\d+)\.html$ /index.php?mod=category&cid=$1&page=$2;
rewrite ^/category/(\d+)-(.+)-(\d+)\.html$ /index.php?mod=category&cid=$1&sort=$2&page=$3;

# 排行榜页面
rewrite ^/top/(\d+)\.html$ /index.php?mod=top&cid=$1;
rewrite ^/top/(\d+)-(\d+)\.html$ /index.php?mod=top&cid=$1&page=$2;

# 默认处理
if (!-e $request_filename) {
    rewrite ^ /index.php last;
}